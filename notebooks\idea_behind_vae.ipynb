{"cells": [{"cell_type": "code", "execution_count": 17, "metadata": {}, "outputs": [], "source": ["import torch\n", "import torch.nn.functional as F\n", "import torch.nn as nn\n", "import numpy as np\n", "import matplotlib.pyplot as plt\n", "from tqdm.notebook import tqdm"]}, {"cell_type": "markdown", "metadata": {}, "source": ["# First pass\n", "\n", "At a high level, a VAE is just an Autoencoder with some extra stuff to make the latent space better.\n", "\n", "An autoencoder $f(x)$ simply takes an input $x$ and produces a reconstruction of $x$ as $\\hat{x}$. It distills $x$ into less numbers as a compression (bottleneck) as $$f(x) = \\text{decode}(\\text{encode}(x))$$ where encode and decode are both learnable functions.\n", "\n", "With VAEs, the encode function produces a probability distribution rather than numbers that the decoder uses directly. At the encoding step, we use the probability distribution to randomly sample the vector, which then gets decoded by the decoder. "]}, {"cell_type": "code", "execution_count": 32, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["x tensor([[ 0.3014, -0.9764, -0.0254, -0.1521, -0.0268,  0.6461,  0.6105,  0.9971,\n", "          0.4083,  0.5957]])\n", "x_hat tensor([[-0.3292,  0.0935,  0.4351,  0.1092, -0.1609,  0.0980,  0.0332,  0.2981,\n", "          0.3660,  0.3332]])\n", "reconstruction loss tensor(0.3033)\n"]}], "source": ["# Example of untrained vanilla autoencoder\n", "# with an input of 10 numbers and tries to reconstruct 10 numbers (not very well since not trained)\n", "with torch.no_grad():\n", "\ttorch.manual_seed(0)\n", "\tencoder = torch.nn.Sequential(\n", "\t\tnn.<PERSON><PERSON>(10, 5),\n", "\t\tnn.ReLU(),\n", "\t\tnn.<PERSON><PERSON>(5, 2),\n", "\t)\n", "\tdecoder = nn.Sequential(\n", "\t\tnn.<PERSON><PERSON>(2, 5),\n", "\t\tnn.ReLU(),\n", "\t\tnn.<PERSON><PERSON>(5, 10),\n", "\t)\n", "\tx = torch.randn((1, 10))\n", "\tf = lambda x: decoder(encoder(x))\n", "\tx_hat = f(x)\n", "\tprint(\"x\", x)\n", "\tprint(\"x_hat\", x_hat)\n", "\tprint(\"reconstruction loss\", F.mse_loss(x_hat, x))"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Second Pass\n", "\n", "Why does turning the bottleneck into a probability distribution help? And how can we represent that in math and pytorch?\n", "\n", "The logic is that Autoencoders can embed their vectors anywhere in space. So the output shape might be terrible for us humans to reason about. Additionally, vector math and interpolation between vectors in the latent space, might not be as smooth as we'd want. \n", "\n", "We humans deal with lines, whereas the autoencoder can embed a very non-linear latent space that is understandable to the autoencoder, but not to us.\n", "\n", "> [!NOTE]\n", "> You could in theory run a secondary Sparse Autoencoder (SAE) to interpolate a vanilla autoencoders latent space if it's not great to look at. This is redundant. Let's just use a VAE.\n", "\n", "In essence, instead of outputting a vector that is directly decoded as $$\\vec{v} = \\begin{bmatrix}v_0\\\\v_1\\\\\\vdots\\\\v_n\\end{bmatrix}$$ where $n$ is the size of the latent space $\\mathbb{R}^n$, we instead can represent the output as the parameters in a probability distribution and take $\\vec{v}$ as a sample from the constructed distribution.\n", "\n", "For example, we could output two vectors in the latent space as\n", "$$\n", "\\begin{align}\n", "\t\\vec{\\mu} &= \\begin{bmatrix}\\mu_0\\\\\\mu_1\\\\\\vdots\\\\\\mu_n\\end{bmatrix}\\\\\n", "\t\\vec{\\sigma} &= \\begin{bmatrix}\\sigma_0\\\\\\sigma_1\\\\\\vdots\\\\\\sigma_n\\end{bmatrix}\n", "\\end{align}\n", "$$\n", "\n", "where each pair of matching indices from $\\vec{\\mu}$ and $\\vec{\\sigma}$ are then sampled from a Gaussian distribution to recreate the $\\vec{v}$ as\n", "$$\n", "\\begin{align}\n", "\t\\vec{v} &= \\begin{bmatrix}\n", "\t\t\t\t\tv_0 \\sim N(\\mu_0, \\sigma_0^2)\\\\\n", "\t\t\t\t\tv_1 \\sim N(\\mu_1, \\sigma_1^2)\\\\\n", "\t\t\t\t\t\\vdots\\\\\n", "\t\t\t\t\tv_n \\sim N(\\mu_n, \\sigma_n^2)\\\\\n", "\t\t\t\t\\end{bmatrix}\\\\\n", "\\end{align}\n", "$$ \n", "\n", "using a random sampler during inference time. This way, each value in $\\vec{v}$ is a continuous function with a value realized at inference time. The latent space created from these outputs looks much more normally distributed (duh) and nicer to work with and do vector math on. Making the VAE more useful in more cases than the regular AE.\n", "\n", "Note that the standard deviations need to be positive so we can apply an activation like ReLU or softplus to the standard deviation functions in the latent space before sampling.\n", "\n", "Note that since we sample to create the vector, we have to create a pathway for the gradient that doesn't terminate. How does one backprop through a random number generator? The workaround is called the reparameterization trick.\n", "\n", "This is where use the fact that a normal distribution can be represented as the standard deviation times a standard normal distribution + the mean. So we have that $$\\vec{v} = \\mu + \\sigma \\epsilon$$ where $\\epsilon \\sim N(0, 1)$."]}, {"cell_type": "code", "execution_count": 42, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["SmallVAE(\n", "  (enc_fc1): Linear(in_features=10, out_features=5, bias=True)\n", "  (enc_mu): Linear(in_features=5, out_features=2, bias=True)\n", "  (enc_sig): Linear(in_features=5, out_features=2, bias=True)\n", "  (dec_fc1): Linear(in_features=2, out_features=5, bias=True)\n", "  (dec_fc2): Linear(in_features=5, out_features=10, bias=True)\n", ")\n", "x tensor([[ 0.6105,  0.9971,  0.4083,  0.5957, -0.8193,  0.5156,  0.4135,  0.9219,\n", "         -1.3676, -0.1525]])\n", "x_hat tensor([[-0.3887,  0.0052, -0.3608,  0.5877, -0.3170, -0.2160, -0.8451, -0.4282,\n", "         -0.0110,  0.8447]])\n"]}], "source": ["# now let me change the example before to include the normal distributions \n", "class SmallVAE(nn.Module):\n", "\tdef __init__(self):\n", "\t\tsuper().__init__()\n", "\t\t# encoder\n", "\t\tself.enc_fc1 = nn.Linear(10, 5)\n", "\t\t# prob dist (replaces the latent embedder)\n", "\t\tself.enc_mu = nn.Linear(5, 2)\n", "\t\tself.enc_sig = nn.Linear(5, 2)\n", "\n", "\t\t# decoder\n", "\t\tself.dec_fc1 = nn.Linear(2, 5)\n", "\t\tself.dec_fc2 = nn.Linear(5, 10)\n", "\t\t\n", "\tdef forward(self, x):\n", "\t\t# encoder\n", "\t\tx = self.enc_fc1(x)\n", "\t\tx = <PERSON>.relu(x)\n", "\t\tsig, mu = self.enc_sig(x), self.enc_mu(x)\n", "\t\tsig = F.softplus(sig) # standard deviation must be positive, mean can be anything\n", "\n", "\t\teps = torch.randn_like(sig) # standard normal\n", "\t\tx = mu + sig*eps # reconstruct the vector from sample\n", "\n", "\t\t# decoder\n", "\t\tx = self.dec_fc1(x)\n", "\t\tx = <PERSON>.relu(x)\n", "\t\tx = self.dec_fc2(x)\n", "\n", "\t\treturn x\n", "\n", "with torch.no_grad():\n", "\ttorch.manual_seed(0)\n", "\tf_vae = SmallVAE()\n", "\tx = torch.randn((1, 10))\n", "\tx_hat = f_vae(x)\n", "\n", "\tprint(f_vae)\n", "\tprint(\"x\", x)\n", "\tprint(\"x_hat\", x_hat)\n", "\n"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Third Pass\n", "\n", "Actually, keeping the sigma values positive with softplus or ReLU is a bit of a hack. We can simply change the space of values we're working with and undo this function to reclaim the sigma's whenever we want. This is known as the logvar trick.\n", "\n", "If we simply assume that the values we get from the latent layer is $l = \\ln(\\sigma^2)$, then if we expo and divide by 2 we get  $e^{l/2} = \\sigma$ which essentially allows $l$ to be positive or negative, and we simply retrieve the $\\sigma$ when we need it. Note that since we'll need the $\\ln(\\sigma^2)$ in the loss function we can also directly use this value during training.\n", "\n", "How does this trick differ to just considering $l$ as $\\sigma$ and applying a positive activation function (ReLU or Softplus)? Look below. The function are remarkably similar. So it likely doesn't even matter that much."]}, {"cell_type": "code", "execution_count": 56, "metadata": {}, "outputs": [{"data": {"image/png": "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", "text/plain": ["<Figure size 640x480 with 1 Axes>"]}, "metadata": {}, "output_type": "display_data"}], "source": ["xs = torch.linspace(-5, 5, 100)\n", "sp = F.softplus(xs)\n", "log_var = torch.exp(0.5*xs)\n", "relu = torch.relu(xs)\n", "plt.plot(xs, sp, label=\"Softplus\")\n", "plt.plot(xs, log_var, label=\"$e^{1/2 x}$ (log var trick)\")\n", "plt.plot(xs, relu, label=\"ReLU\")\n", "plt.grid()\n", "plt.legend()\n", "plt.show()"]}, {"cell_type": "code", "execution_count": 60, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["V2SmallVAE(\n", "  (enc_fc1): Linear(in_features=10, out_features=5, bias=True)\n", "  (enc_mu): Linear(in_features=5, out_features=2, bias=True)\n", "  (enc_log_var): Linear(in_features=5, out_features=2, bias=True)\n", "  (dec_fc1): Linear(in_features=2, out_features=5, bias=True)\n", "  (dec_fc2): Linear(in_features=5, out_features=10, bias=True)\n", ")\n", "x tensor([[ 0.6105,  0.9971,  0.4083,  0.5957, -0.8193,  0.5156,  0.4135,  0.9219,\n", "         -1.3676, -0.1525]])\n", "x_hat tensor([[-0.3794,  0.0409, -0.4476,  0.7238, -0.3175, -0.1835, -0.8880, -0.4067,\n", "          0.0084,  0.8928]])\n"]}], "source": ["# How would this change in the code? Super easily!\n", "\n", "class V2SmallVAE(nn.Module):\n", "\tdef __init__(self):\n", "\t\tsuper().__init__()\n", "\t\t# encoder\n", "\t\tself.enc_fc1 = nn.Linear(10, 5)\n", "\t\t# prob dist (replaces the latent embedder)\n", "\t\tself.enc_mu = nn.Linear(5, 2)\n", "\t\tself.enc_log_var = nn.Linear(5, 2) # assume \\ln(\\sigma^2)\n", "\n", "\t\t# decoder\n", "\t\tself.dec_fc1 = nn.Linear(2, 5)\n", "\t\tself.dec_fc2 = nn.Linear(5, 10)\n", "\t\t\n", "\tdef forward(self, x):\n", "\t\t# encoder\n", "\t\tx = self.enc_fc1(x)\n", "\t\tx = <PERSON>.relu(x)\n", "\n", "\t\t# latent space\n", "\t\tlog_var, mu = self.enc_log_var(x), self.enc_mu(x)\n", "\t\tsig = torch.exp(0.5*log_var) # e^{1/2 ln(\\sigma^2)} = e^{1/2 2ln(\\sigma)} =  \\sigma\n", "\t\teps = torch.randn_like(mu) # standard normal\n", "\t\tx = mu + sig*eps # reconstruct the vector from sample\n", "\n", "\t\t# decoder\n", "\t\tx = self.dec_fc1(x)\n", "\t\tx = <PERSON>.relu(x)\n", "\t\tx = self.dec_fc2(x)\n", "\n", "\t\treturn x\n", "\n", "with torch.no_grad():\n", "\ttorch.manual_seed(0)\n", "\tf_vae = V2SmallVAE()\n", "\tx = torch.randn((1, 10))\n", "\tx_hat = f_vae(x)\n", "\n", "\tprint(f_vae)\n", "\tprint(\"x\", x)\n", "\tprint(\"x_hat\", x_hat)\n"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Fourth Pass\n", "\n", "What does the VAE loss function look like? Well it looks pretty complicated!\n", "\n", "The loss has two pieces: \n", "1. KL divergence between the distribution created from the sigma and mu versus a standard normal (like a regularization term such that the latent space is nicer)\n", "2. A reconstruction loss of the input $x$ versus the created output $\\hat{x}$ from the decoder.\n", "\n", "We get these terms through variational inference and MLE of the params. After all that we end up with the total loss as\n", "\n", "$$\n", "\\begin{align}\n", "\tL(\\theta) &= D_{kl}\\left(q(z|x) || N(0, 1)\\right) - \\sum \\log(p(x|z))\\\\\n", "\t\t\t  &= \\left[{\\color{orange}\\frac{1}{2} \\sum \\left(  1 + \\log(\\sigma^2) - \\mu^2 - \\sigma^2\\right)} \\right] + \\left[{ \\color{cyan} \\sum \\log(p(x|z)) } \\right]\\\\\n", "\\end{align}\n", "$$\n", "\n", "Which is really just a $\\text{\\color{orange} constraint on the latent space to be normally distributed}$ and the $\\text{\\color{cyan} reconstruction should be close to the original}.$\n", "\n", "Then we can minimize this by making it negative and performing gradient descent! Over a mini batch we can just average over the negative likelyhood.\n", "\n", "The reconstruction loss can be anything you really want. For example MSE (mean squared error) which might actually yield better results. You also will probably need to weigh the losses such that the kl divergence doesn't dominate under certain reconstruction losses. People have added a $\\beta$ term that multiplies the $\\color{orange}\\text{kl}$ term to relax the latent space a bit."]}, {"cell_type": "code", "execution_count": 73, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["V3SmallVAE(\n", "  (enc_fc1): Linear(in_features=10, out_features=5, bias=True)\n", "  (enc_mu): Linear(in_features=5, out_features=2, bias=True)\n", "  (enc_log_var): Linear(in_features=5, out_features=2, bias=True)\n", "  (dec_fc1): Linear(in_features=2, out_features=5, bias=True)\n", "  (dec_fc2): Linear(in_features=5, out_features=10, bias=True)\n", ")\n", "x tensor([[ 1.6268,  0.1723, -1.6115, -0.4794,  0.1970, -1.1773, -0.0661, -0.3584,\n", "         -1.5616, -0.3546],\n", "        [ 1.0811,  0.1315, -0.2473, -1.4154, -1.0787, -0.7209,  1.4708,  0.2756,\n", "          0.6668, -0.9944]])\n", "x_hat tensor([[-0.6499,  0.1503, -0.0959,  0.2189, -0.5204, -0.1421, -0.8821, -0.5105,\n", "         -0.0693,  0.9006],\n", "        [-1.0407,  0.5587, -0.0711,  0.2690, -0.8563,  0.1390, -1.1471, -0.5414,\n", "         -0.0708,  1.2174]])\n", "loss tensor(2.3631)\n"]}], "source": ["# Lets create the loss function as part of the forward pass!\n", "class V3SmallVAE(nn.Module):\n", "\tdef __init__(self):\n", "\t\tsuper().__init__()\n", "\t\t# encoder\n", "\t\tself.enc_fc1 = nn.Linear(10, 5)\n", "\t\t# prob dist (replaces the latent embedder)\n", "\t\tself.enc_mu = nn.Linear(5, 2)\n", "\t\tself.enc_log_var = nn.Linear(5, 2) # assume \\ln(\\sigma^2)\n", "\n", "\t\t# decoder\n", "\t\tself.dec_fc1 = nn.Linear(2, 5)\n", "\t\tself.dec_fc2 = nn.Linear(5, 10)\n", "\t\t\n", "\tdef forward(self, x):\n", "\t\t# encoder\n", "\t\tx_hat = self.enc_fc1(x)\n", "\t\tx_hat = <PERSON>.relu(x_hat)\n", "\n", "\t\t# latent space\n", "\t\tlog_var, mu = self.enc_log_var(x_hat), self.enc_mu(x_hat)\n", "\t\tsig = torch.exp(0.5*log_var) # e^{1/2 ln(\\sigma^2)} = e^{1/2 2ln(\\sigma)} =  \\sigma\n", "\t\teps = torch.randn_like(mu) # standard normal\n", "\t\tx_hat = mu + sig*eps # reconstruct the vector from sample\n", "\n", "\t\t# decoder\n", "\t\tx_hat = self.dec_fc1(x_hat)\n", "\t\tx_hat = <PERSON>.relu(x_hat)\n", "\t\tx_hat = self.dec_fc2(x_hat)\n", "\n", "\t\trecon_loss = F.mse_loss(x_hat, x) \n", "\t\tlatent_loss = -0.5*torch.sum(1 + log_var - mu**2 - sig**2, dim=-1) # KL divergence from VAE paper (see references below)\n", "\t\tlatent_loss = torch.mean(latent_loss, dim=0) # avg. over batches\n", "\t\tloss = recon_loss + latent_loss\n", "\n", "\t\treturn x_hat, loss\n", "\n", "with torch.no_grad():\n", "\ttorch.manual_seed(0)\n", "\tf_vae = V3SmallVAE()\n", "\tx = torch.randn((2, 10))\n", "\tx_hat, loss = f_vae(x)\n", "\n", "\tprint(f_vae)\n", "\tprint(\"x\", x)\n", "\tprint(\"x_hat\", x_hat)\n", "\tprint(\"loss\", loss)\n"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## References\n", "\n", "- https://www.ibm.com/think/topics/variational-autoencoder#:~:text=Variational%20autoencoders%20(VAEs)%20are%20generative,other%20autoencoders%2C%20such%20as%20denoising. (good overview)\n", "- https://www.youtube.com/watch?v=9zKuYvjFFS8 (softplus on standard deviation encoding)\n", "- https://arxiv.org/abs/1312.6114 (original VAE paper)\n", "- https://www.youtube.com/watch?v=afNuE5z2CQ8 (log var trick)"]}], "metadata": {"kernelspec": {"display_name": "Python 3", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.10.14"}}, "nbformat": 4, "nbformat_minor": 2}