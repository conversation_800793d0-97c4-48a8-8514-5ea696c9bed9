<img src="public/logo.svg"  width="400"/>

Learn how Variational Autoencoders (VAE) work by visualizing one running in your browser. 

- Live Site: https://xnought.github.io/vae-explainer/
- Paper: https://arxiv.org/abs/2409.09011
- Also interetested in VQ-VAEs? Check out https://xnought.github.io/vq-vae-explainer/


https://github.com/user-attachments/assets/ef788f0a-9c66-48fb-99d9-f4feb62ca9cd



## Development

```bash
pnpm install
pnpm dev
```
which runs the hot-reloading dev server at http://localhost:5173

## Cite

```bibtex
@misc{bertucci2024vaeexplainer,
      title={VAE Explainer: Supplement Learning Variational Autoencoders with Interactive Visualization}, 
      author={<PERSON> and <PERSON>},
      year={2024},
      eprint={2409.09011},
      archivePrefix={arXiv},
      primaryClass={cs.HC},
      url={https://arxiv.org/abs/2409.09011}, 
}
```

## Code References

- https://keras.io/examples/generative/vae/
- https://www.ibm.com/think/topics/variational-autoencoder#:~:text=Variational%20autoencoders%20(VAEs)%20are%20generative,other%20autoencoders%2C%20such%20as%20denoising.
- https://www.youtube.com/watch?v=9zKuYvjFFS8
- https://arxiv.org/abs/1312.6114 (original VAE paper)
- https://www.youtube.com/watch?v=afNuE5z2CQ8
- https://keras.io/examples/keras_recipes/trainer_pattern/
