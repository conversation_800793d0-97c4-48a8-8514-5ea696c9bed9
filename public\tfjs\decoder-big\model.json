{"format": "graph-model", "generatedBy": "2.16.2", "convertedBy": "TensorFlow.js Converter v4.20.0", "signature": {"inputs": {"inputs": {"name": "inputs:0", "dtype": "DT_FLOAT", "tensorShape": {"dim": [{"size": "-1"}, {"size": "2"}]}}}, "outputs": {"output_0": {"name": "Identity:0", "dtype": "DT_FLOAT", "tensorShape": {"dim": [{"size": "-1"}, {"size": "28"}, {"size": "28"}, {"size": "1"}]}}}}, "modelTopology": {"node": [{"name": "StatefulPartitionedCall/sequential_48_1/conv2d_transpose_40_1/strided_slice/stack", "op": "Const", "attr": {"value": {"tensor": {"dtype": "DT_INT32", "tensorShape": {"dim": [{"size": "1"}]}}}, "dtype": {"type": "DT_INT32"}}}, {"name": "StatefulPartitionedCall/sequential_48_1/conv2d_transpose_40_1/strided_slice/stack_1", "op": "Const", "attr": {"value": {"tensor": {"dtype": "DT_INT32", "tensorShape": {"dim": [{"size": "1"}]}}}, "dtype": {"type": "DT_INT32"}}}, {"name": "StatefulPartitionedCall/sequential_48_1/conv2d_transpose_40_1/strided_slice/stack_2", "op": "Const", "attr": {"value": {"tensor": {"dtype": "DT_INT32", "tensorShape": {"dim": [{"size": "1"}]}}}, "dtype": {"type": "DT_INT32"}}}, {"name": "StatefulPartitionedCall/sequential_48_1/conv2d_transpose_40_1/conv_transpose/input_sizes/1", "op": "Const", "attr": {"value": {"tensor": {"dtype": "DT_INT32", "tensorShape": {}}}, "dtype": {"type": "DT_INT32"}}}, {"name": "StatefulPartitionedCall/sequential_48_1/conv2d_transpose_40_1/conv_transpose/input_sizes/2", "op": "Const", "attr": {"value": {"tensor": {"dtype": "DT_INT32", "tensorShape": {}}}, "dtype": {"type": "DT_INT32"}}}, {"name": "StatefulPartitionedCall/sequential_48_1/conv2d_transpose_40_1/conv_transpose/input_sizes/3", "op": "Const", "attr": {"value": {"tensor": {"dtype": "DT_INT32", "tensorShape": {}}}, "dtype": {"type": "DT_INT32"}}}, {"name": "StatefulPartitionedCall/sequential_48_1/conv2d_transpose_40_1/conv_transpose/ReadVariableOp", "op": "Const", "attr": {"value": {"tensor": {"dtype": "DT_FLOAT", "tensorShape": {"dim": [{"size": "3"}, {"size": "3"}, {"size": "1"}, {"size": "32"}]}}}, "dtype": {"type": "DT_FLOAT"}}}, {"name": "StatefulPartitionedCall/sequential_48_1/conv2d_transpose_39_1/strided_slice/stack", "op": "Const", "attr": {"value": {"tensor": {"dtype": "DT_INT32", "tensorShape": {"dim": [{"size": "1"}]}}}, "dtype": {"type": "DT_INT32"}}}, {"name": "StatefulPartitionedCall/sequential_48_1/conv2d_transpose_39_1/strided_slice/stack_1", "op": "Const", "attr": {"value": {"tensor": {"dtype": "DT_INT32", "tensorShape": {"dim": [{"size": "1"}]}}}, "dtype": {"type": "DT_INT32"}}}, {"name": "StatefulPartitionedCall/sequential_48_1/conv2d_transpose_39_1/strided_slice/stack_2", "op": "Const", "attr": {"value": {"tensor": {"dtype": "DT_INT32", "tensorShape": {"dim": [{"size": "1"}]}}}, "dtype": {"type": "DT_INT32"}}}, {"name": "StatefulPartitionedCall/sequential_48_1/conv2d_transpose_39_1/conv_transpose/input_sizes/1", "op": "Const", "attr": {"value": {"tensor": {"dtype": "DT_INT32", "tensorShape": {}}}, "dtype": {"type": "DT_INT32"}}}, {"name": "StatefulPartitionedCall/sequential_48_1/conv2d_transpose_39_1/conv_transpose/input_sizes/2", "op": "Const", "attr": {"value": {"tensor": {"dtype": "DT_INT32", "tensorShape": {}}}, "dtype": {"type": "DT_INT32"}}}, {"name": "StatefulPartitionedCall/sequential_48_1/conv2d_transpose_39_1/conv_transpose/input_sizes/3", "op": "Const", "attr": {"value": {"tensor": {"dtype": "DT_INT32", "tensorShape": {}}}, "dtype": {"type": "DT_INT32"}}}, {"name": "StatefulPartitionedCall/sequential_48_1/conv2d_transpose_39_1/conv_transpose/ReadVariableOp", "op": "Const", "attr": {"value": {"tensor": {"dtype": "DT_FLOAT", "tensorShape": {"dim": [{"size": "3"}, {"size": "3"}, {"size": "32"}, {"size": "64"}]}}}, "dtype": {"type": "DT_FLOAT"}}}, {"name": "StatefulPartitionedCall/sequential_48_1/conv2d_transpose_38_1/strided_slice/stack", "op": "Const", "attr": {"value": {"tensor": {"dtype": "DT_INT32", "tensorShape": {"dim": [{"size": "1"}]}}}, "dtype": {"type": "DT_INT32"}}}, {"name": "StatefulPartitionedCall/sequential_48_1/conv2d_transpose_38_1/strided_slice/stack_1", "op": "Const", "attr": {"value": {"tensor": {"dtype": "DT_INT32", "tensorShape": {"dim": [{"size": "1"}]}}}, "dtype": {"type": "DT_INT32"}}}, {"name": "StatefulPartitionedCall/sequential_48_1/conv2d_transpose_38_1/strided_slice/stack_2", "op": "Const", "attr": {"value": {"tensor": {"dtype": "DT_INT32", "tensorShape": {"dim": [{"size": "1"}]}}}, "dtype": {"type": "DT_INT32"}}}, {"name": "StatefulPartitionedCall/sequential_48_1/conv2d_transpose_38_1/conv_transpose/input_sizes/1", "op": "Const", "attr": {"value": {"tensor": {"dtype": "DT_INT32", "tensorShape": {}}}, "dtype": {"type": "DT_INT32"}}}, {"name": "StatefulPartitionedCall/sequential_48_1/conv2d_transpose_38_1/conv_transpose/input_sizes/2", "op": "Const", "attr": {"value": {"tensor": {"dtype": "DT_INT32", "tensorShape": {}}}, "dtype": {"type": "DT_INT32"}}}, {"name": "StatefulPartitionedCall/sequential_48_1/conv2d_transpose_38_1/conv_transpose/input_sizes/3", "op": "Const", "attr": {"value": {"tensor": {"dtype": "DT_INT32", "tensorShape": {}}}, "dtype": {"type": "DT_INT32"}}}, {"name": "StatefulPartitionedCall/sequential_48_1/conv2d_transpose_38_1/conv_transpose/ReadVariableOp", "op": "Const", "attr": {"value": {"tensor": {"dtype": "DT_FLOAT", "tensorShape": {"dim": [{"size": "3"}, {"size": "3"}, {"size": "64"}, {"size": "64"}]}}}, "dtype": {"type": "DT_FLOAT"}}}, {"name": "StatefulPartitionedCall/sequential_48_1/dense_63_1/Cast/ReadVariableOp", "op": "Const", "attr": {"value": {"tensor": {"dtype": "DT_FLOAT", "tensorShape": {"dim": [{"size": "2"}, {"size": "3136"}]}}}, "dtype": {"type": "DT_FLOAT"}}}, {"name": "StatefulPartitionedCall/sequential_48_1/dense_63_1/Add/ReadVariableOp", "op": "Const", "attr": {"value": {"tensor": {"dtype": "DT_FLOAT", "tensorShape": {"dim": [{"size": "3136"}]}}}, "dtype": {"type": "DT_FLOAT"}}}, {"name": "StatefulPartitionedCall/sequential_48_1/reshape_13_1/strided_slice/stack", "op": "Const", "attr": {"value": {"tensor": {"dtype": "DT_INT32", "tensorShape": {"dim": [{"size": "1"}]}}}, "dtype": {"type": "DT_INT32"}}}, {"name": "StatefulPartitionedCall/sequential_48_1/reshape_13_1/strided_slice/stack_1", "op": "Const", "attr": {"value": {"tensor": {"dtype": "DT_INT32", "tensorShape": {"dim": [{"size": "1"}]}}}, "dtype": {"type": "DT_INT32"}}}, {"name": "StatefulPartitionedCall/sequential_48_1/reshape_13_1/strided_slice/stack_2", "op": "Const", "attr": {"value": {"tensor": {"dtype": "DT_INT32", "tensorShape": {"dim": [{"size": "1"}]}}}, "dtype": {"type": "DT_INT32"}}}, {"name": "StatefulPartitionedCall/sequential_48_1/reshape_13_1/Reshape/shape/1", "op": "Const", "attr": {"value": {"tensor": {"dtype": "DT_INT32", "tensorShape": {}}}, "dtype": {"type": "DT_INT32"}}}, {"name": "StatefulPartitionedCall/sequential_48_1/reshape_13_1/Reshape/shape/2", "op": "Const", "attr": {"value": {"tensor": {"dtype": "DT_INT32", "tensorShape": {}}}, "dtype": {"type": "DT_INT32"}}}, {"name": "StatefulPartitionedCall/sequential_48_1/reshape_13_1/Reshape/shape/3", "op": "Const", "attr": {"value": {"tensor": {"dtype": "DT_INT32", "tensorShape": {}}}, "dtype": {"type": "DT_INT32"}}}, {"name": "StatefulPartitionedCall/sequential_48_1/conv2d_transpose_38_1/Reshape", "op": "Const", "attr": {"value": {"tensor": {"dtype": "DT_FLOAT", "tensorShape": {"dim": [{"size": "1"}, {"size": "1"}, {"size": "1"}, {"size": "64"}]}}}, "dtype": {"type": "DT_FLOAT"}}}, {"name": "StatefulPartitionedCall/sequential_48_1/conv2d_transpose_39_1/Reshape", "op": "Const", "attr": {"value": {"tensor": {"dtype": "DT_FLOAT", "tensorShape": {"dim": [{"size": "1"}, {"size": "1"}, {"size": "1"}, {"size": "32"}]}}}, "dtype": {"type": "DT_FLOAT"}}}, {"name": "StatefulPartitionedCall/sequential_48_1/conv2d_transpose_40_1/Reshape", "op": "Const", "attr": {"value": {"tensor": {"dtype": "DT_FLOAT", "tensorShape": {"dim": [{"size": "1"}, {"size": "1"}, {"size": "1"}, {"size": "1"}]}}}, "dtype": {"type": "DT_FLOAT"}}}, {"name": "inputs", "op": "Placeholder", "attr": {"shape": {"shape": {"dim": [{"size": "-1"}, {"size": "2"}]}}, "dtype": {"type": "DT_FLOAT"}}}, {"name": "StatefulPartitionedCall/sequential_48_1/dense_63_1/Relu", "op": "_FusedMatMul", "input": ["inputs", "StatefulPartitionedCall/sequential_48_1/dense_63_1/Cast/ReadVariableOp", "StatefulPartitionedCall/sequential_48_1/dense_63_1/Add/ReadVariableOp"], "device": "/device:CPU:0", "attr": {"transpose_a": {"b": false}, "transpose_b": {"b": false}, "fused_ops": {"list": {"s": ["Qmlhc0FkZA==", "UmVsdQ=="]}}, "leakyrelu_alpha": {"f": 0.2}, "num_args": {"i": "1"}, "epsilon": {"f": 0.0}, "T": {"type": "DT_FLOAT"}}}, {"name": "StatefulPartitionedCall/sequential_48_1/reshape_13_1/Shape", "op": "<PERSON><PERSON><PERSON>", "input": ["StatefulPartitionedCall/sequential_48_1/dense_63_1/Relu"], "attr": {"T": {"type": "DT_FLOAT"}, "out_type": {"type": "DT_INT32"}}, "experimentalType": {"typeId": "TFT_PRODUCT", "args": [{"typeId": "TFT_SHAPE_TENSOR", "args": [{"typeId": "TFT_INT32"}]}]}}, {"name": "StatefulPartitionedCall/sequential_48_1/reshape_13_1/strided_slice", "op": "StridedSlice", "input": ["StatefulPartitionedCall/sequential_48_1/reshape_13_1/Shape", "StatefulPartitionedCall/sequential_48_1/reshape_13_1/strided_slice/stack", "StatefulPartitionedCall/sequential_48_1/reshape_13_1/strided_slice/stack_1", "StatefulPartitionedCall/sequential_48_1/reshape_13_1/strided_slice/stack_2"], "attr": {"shrink_axis_mask": {"i": "1"}, "Index": {"type": "DT_INT32"}, "new_axis_mask": {"i": "0"}, "begin_mask": {"i": "0"}, "end_mask": {"i": "0"}, "T": {"type": "DT_INT32"}, "ellipsis_mask": {"i": "0"}}}, {"name": "StatefulPartitionedCall/sequential_48_1/reshape_13_1/Reshape/shape", "op": "Pack", "input": ["StatefulPartitionedCall/sequential_48_1/reshape_13_1/strided_slice", "StatefulPartitionedCall/sequential_48_1/reshape_13_1/Reshape/shape/1", "StatefulPartitionedCall/sequential_48_1/reshape_13_1/Reshape/shape/2", "StatefulPartitionedCall/sequential_48_1/reshape_13_1/Reshape/shape/3"], "attr": {"N": {"i": "4"}, "axis": {"i": "0"}, "T": {"type": "DT_INT32"}}}, {"name": "StatefulPartitionedCall/sequential_48_1/reshape_13_1/Reshape", "op": "Reshape", "input": ["StatefulPartitionedCall/sequential_48_1/dense_63_1/Relu", "StatefulPartitionedCall/sequential_48_1/reshape_13_1/Reshape/shape"], "attr": {"T": {"type": "DT_FLOAT"}, "Tshape": {"type": "DT_INT32"}}}, {"name": "StatefulPartitionedCall/sequential_48_1/conv2d_transpose_38_1/Shape", "op": "<PERSON><PERSON><PERSON>", "input": ["StatefulPartitionedCall/sequential_48_1/reshape_13_1/Reshape"], "attr": {"T": {"type": "DT_FLOAT"}, "out_type": {"type": "DT_INT32"}}, "experimentalType": {"typeId": "TFT_PRODUCT", "args": [{"typeId": "TFT_SHAPE_TENSOR", "args": [{"typeId": "TFT_INT32"}]}]}}, {"name": "StatefulPartitionedCall/sequential_48_1/conv2d_transpose_38_1/strided_slice", "op": "StridedSlice", "input": ["StatefulPartitionedCall/sequential_48_1/conv2d_transpose_38_1/Shape", "StatefulPartitionedCall/sequential_48_1/conv2d_transpose_38_1/strided_slice/stack", "StatefulPartitionedCall/sequential_48_1/conv2d_transpose_38_1/strided_slice/stack_1", "StatefulPartitionedCall/sequential_48_1/conv2d_transpose_38_1/strided_slice/stack_2"], "attr": {"shrink_axis_mask": {"i": "1"}, "Index": {"type": "DT_INT32"}, "new_axis_mask": {"i": "0"}, "begin_mask": {"i": "0"}, "T": {"type": "DT_INT32"}, "end_mask": {"i": "0"}, "ellipsis_mask": {"i": "0"}}}, {"name": "StatefulPartitionedCall/sequential_48_1/conv2d_transpose_38_1/conv_transpose/input_sizes", "op": "Pack", "input": ["StatefulPartitionedCall/sequential_48_1/conv2d_transpose_38_1/strided_slice", "StatefulPartitionedCall/sequential_48_1/conv2d_transpose_38_1/conv_transpose/input_sizes/1", "StatefulPartitionedCall/sequential_48_1/conv2d_transpose_38_1/conv_transpose/input_sizes/2", "StatefulPartitionedCall/sequential_48_1/conv2d_transpose_38_1/conv_transpose/input_sizes/3"], "attr": {"N": {"i": "4"}, "axis": {"i": "0"}, "T": {"type": "DT_INT32"}}}, {"name": "StatefulPartitionedCall/sequential_48_1/conv2d_transpose_38_1/conv_transpose", "op": "Conv2DBackpropInput", "input": ["StatefulPartitionedCall/sequential_48_1/conv2d_transpose_38_1/conv_transpose/input_sizes", "StatefulPartitionedCall/sequential_48_1/conv2d_transpose_38_1/conv_transpose/ReadVariableOp", "StatefulPartitionedCall/sequential_48_1/reshape_13_1/Reshape"], "attr": {"padding": {"s": "U0FNRQ=="}, "data_format": {"s": "TkhXQw=="}, "strides": {"list": {"i": ["1", "2", "2", "1"]}}, "use_cudnn_on_gpu": {"b": true}, "T": {"type": "DT_FLOAT"}, "explicit_paddings": {"list": {}}, "dilations": {"list": {"i": ["1", "1", "1", "1"]}}}}, {"name": "StatefulPartitionedCall/sequential_48_1/conv2d_transpose_38_1/add", "op": "AddV2", "input": ["StatefulPartitionedCall/sequential_48_1/conv2d_transpose_38_1/conv_transpose", "StatefulPartitionedCall/sequential_48_1/conv2d_transpose_38_1/Reshape"], "attr": {"T": {"type": "DT_FLOAT"}}}, {"name": "StatefulPartitionedCall/sequential_48_1/conv2d_transpose_38_1/Relu", "op": "<PERSON><PERSON>", "input": ["StatefulPartitionedCall/sequential_48_1/conv2d_transpose_38_1/add"], "attr": {"T": {"type": "DT_FLOAT"}}}, {"name": "StatefulPartitionedCall/sequential_48_1/conv2d_transpose_39_1/Shape", "op": "<PERSON><PERSON><PERSON>", "input": ["StatefulPartitionedCall/sequential_48_1/conv2d_transpose_38_1/Relu"], "attr": {"T": {"type": "DT_FLOAT"}, "out_type": {"type": "DT_INT32"}}, "experimentalType": {"typeId": "TFT_PRODUCT", "args": [{"typeId": "TFT_SHAPE_TENSOR", "args": [{"typeId": "TFT_INT32"}]}]}}, {"name": "StatefulPartitionedCall/sequential_48_1/conv2d_transpose_39_1/strided_slice", "op": "StridedSlice", "input": ["StatefulPartitionedCall/sequential_48_1/conv2d_transpose_39_1/Shape", "StatefulPartitionedCall/sequential_48_1/conv2d_transpose_39_1/strided_slice/stack", "StatefulPartitionedCall/sequential_48_1/conv2d_transpose_39_1/strided_slice/stack_1", "StatefulPartitionedCall/sequential_48_1/conv2d_transpose_39_1/strided_slice/stack_2"], "attr": {"shrink_axis_mask": {"i": "1"}, "new_axis_mask": {"i": "0"}, "Index": {"type": "DT_INT32"}, "begin_mask": {"i": "0"}, "T": {"type": "DT_INT32"}, "end_mask": {"i": "0"}, "ellipsis_mask": {"i": "0"}}}, {"name": "StatefulPartitionedCall/sequential_48_1/conv2d_transpose_39_1/conv_transpose/input_sizes", "op": "Pack", "input": ["StatefulPartitionedCall/sequential_48_1/conv2d_transpose_39_1/strided_slice", "StatefulPartitionedCall/sequential_48_1/conv2d_transpose_39_1/conv_transpose/input_sizes/1", "StatefulPartitionedCall/sequential_48_1/conv2d_transpose_39_1/conv_transpose/input_sizes/2", "StatefulPartitionedCall/sequential_48_1/conv2d_transpose_39_1/conv_transpose/input_sizes/3"], "attr": {"N": {"i": "4"}, "axis": {"i": "0"}, "T": {"type": "DT_INT32"}}}, {"name": "StatefulPartitionedCall/sequential_48_1/conv2d_transpose_39_1/conv_transpose", "op": "Conv2DBackpropInput", "input": ["StatefulPartitionedCall/sequential_48_1/conv2d_transpose_39_1/conv_transpose/input_sizes", "StatefulPartitionedCall/sequential_48_1/conv2d_transpose_39_1/conv_transpose/ReadVariableOp", "StatefulPartitionedCall/sequential_48_1/conv2d_transpose_38_1/Relu"], "attr": {"padding": {"s": "U0FNRQ=="}, "data_format": {"s": "TkhXQw=="}, "strides": {"list": {"i": ["1", "2", "2", "1"]}}, "use_cudnn_on_gpu": {"b": true}, "T": {"type": "DT_FLOAT"}, "explicit_paddings": {"list": {}}, "dilations": {"list": {"i": ["1", "1", "1", "1"]}}}}, {"name": "StatefulPartitionedCall/sequential_48_1/conv2d_transpose_39_1/add", "op": "AddV2", "input": ["StatefulPartitionedCall/sequential_48_1/conv2d_transpose_39_1/conv_transpose", "StatefulPartitionedCall/sequential_48_1/conv2d_transpose_39_1/Reshape"], "attr": {"T": {"type": "DT_FLOAT"}}}, {"name": "StatefulPartitionedCall/sequential_48_1/conv2d_transpose_39_1/Relu", "op": "<PERSON><PERSON>", "input": ["StatefulPartitionedCall/sequential_48_1/conv2d_transpose_39_1/add"], "attr": {"T": {"type": "DT_FLOAT"}}}, {"name": "StatefulPartitionedCall/sequential_48_1/conv2d_transpose_40_1/Shape", "op": "<PERSON><PERSON><PERSON>", "input": ["StatefulPartitionedCall/sequential_48_1/conv2d_transpose_39_1/Relu"], "attr": {"T": {"type": "DT_FLOAT"}, "out_type": {"type": "DT_INT32"}}, "experimentalType": {"typeId": "TFT_PRODUCT", "args": [{"typeId": "TFT_SHAPE_TENSOR", "args": [{"typeId": "TFT_INT32"}]}]}}, {"name": "StatefulPartitionedCall/sequential_48_1/conv2d_transpose_40_1/strided_slice", "op": "StridedSlice", "input": ["StatefulPartitionedCall/sequential_48_1/conv2d_transpose_40_1/Shape", "StatefulPartitionedCall/sequential_48_1/conv2d_transpose_40_1/strided_slice/stack", "StatefulPartitionedCall/sequential_48_1/conv2d_transpose_40_1/strided_slice/stack_1", "StatefulPartitionedCall/sequential_48_1/conv2d_transpose_40_1/strided_slice/stack_2"], "attr": {"shrink_axis_mask": {"i": "1"}, "new_axis_mask": {"i": "0"}, "Index": {"type": "DT_INT32"}, "begin_mask": {"i": "0"}, "T": {"type": "DT_INT32"}, "end_mask": {"i": "0"}, "ellipsis_mask": {"i": "0"}}}, {"name": "StatefulPartitionedCall/sequential_48_1/conv2d_transpose_40_1/conv_transpose/input_sizes", "op": "Pack", "input": ["StatefulPartitionedCall/sequential_48_1/conv2d_transpose_40_1/strided_slice", "StatefulPartitionedCall/sequential_48_1/conv2d_transpose_40_1/conv_transpose/input_sizes/1", "StatefulPartitionedCall/sequential_48_1/conv2d_transpose_40_1/conv_transpose/input_sizes/2", "StatefulPartitionedCall/sequential_48_1/conv2d_transpose_40_1/conv_transpose/input_sizes/3"], "attr": {"N": {"i": "4"}, "axis": {"i": "0"}, "T": {"type": "DT_INT32"}}}, {"name": "StatefulPartitionedCall/sequential_48_1/conv2d_transpose_40_1/conv_transpose", "op": "Conv2DBackpropInput", "input": ["StatefulPartitionedCall/sequential_48_1/conv2d_transpose_40_1/conv_transpose/input_sizes", "StatefulPartitionedCall/sequential_48_1/conv2d_transpose_40_1/conv_transpose/ReadVariableOp", "StatefulPartitionedCall/sequential_48_1/conv2d_transpose_39_1/Relu"], "attr": {"padding": {"s": "U0FNRQ=="}, "data_format": {"s": "TkhXQw=="}, "use_cudnn_on_gpu": {"b": true}, "strides": {"list": {"i": ["1", "1", "1", "1"]}}, "T": {"type": "DT_FLOAT"}, "explicit_paddings": {"list": {}}, "dilations": {"list": {"i": ["1", "1", "1", "1"]}}}}, {"name": "StatefulPartitionedCall/sequential_48_1/conv2d_transpose_40_1/add", "op": "AddV2", "input": ["StatefulPartitionedCall/sequential_48_1/conv2d_transpose_40_1/conv_transpose", "StatefulPartitionedCall/sequential_48_1/conv2d_transpose_40_1/Reshape"], "attr": {"T": {"type": "DT_FLOAT"}}}, {"name": "StatefulPartitionedCall/sequential_48_1/conv2d_transpose_40_1/Sigmoid", "op": "<PERSON><PERSON><PERSON><PERSON>", "input": ["StatefulPartitionedCall/sequential_48_1/conv2d_transpose_40_1/add"], "attr": {"T": {"type": "DT_FLOAT"}}}, {"name": "Identity", "op": "Identity", "input": ["StatefulPartitionedCall/sequential_48_1/conv2d_transpose_40_1/Sigmoid"], "attr": {"T": {"type": "DT_FLOAT"}}}], "library": {}, "versions": {"producer": 1766}}, "weightsManifest": [{"paths": ["group1-shard1of1.bin"], "weights": [{"name": "StatefulPartitionedCall/sequential_48_1/conv2d_transpose_40_1/strided_slice/stack", "shape": [1], "dtype": "int32"}, {"name": "StatefulPartitionedCall/sequential_48_1/conv2d_transpose_40_1/strided_slice/stack_1", "shape": [1], "dtype": "int32"}, {"name": "StatefulPartitionedCall/sequential_48_1/conv2d_transpose_40_1/strided_slice/stack_2", "shape": [1], "dtype": "int32"}, {"name": "StatefulPartitionedCall/sequential_48_1/conv2d_transpose_40_1/conv_transpose/input_sizes/1", "shape": [], "dtype": "int32"}, {"name": "StatefulPartitionedCall/sequential_48_1/conv2d_transpose_40_1/conv_transpose/input_sizes/2", "shape": [], "dtype": "int32"}, {"name": "StatefulPartitionedCall/sequential_48_1/conv2d_transpose_40_1/conv_transpose/input_sizes/3", "shape": [], "dtype": "int32"}, {"name": "StatefulPartitionedCall/sequential_48_1/conv2d_transpose_40_1/conv_transpose/ReadVariableOp", "shape": [3, 3, 1, 32], "dtype": "float32"}, {"name": "StatefulPartitionedCall/sequential_48_1/conv2d_transpose_39_1/strided_slice/stack", "shape": [1], "dtype": "int32"}, {"name": "StatefulPartitionedCall/sequential_48_1/conv2d_transpose_39_1/strided_slice/stack_1", "shape": [1], "dtype": "int32"}, {"name": "StatefulPartitionedCall/sequential_48_1/conv2d_transpose_39_1/strided_slice/stack_2", "shape": [1], "dtype": "int32"}, {"name": "StatefulPartitionedCall/sequential_48_1/conv2d_transpose_39_1/conv_transpose/input_sizes/1", "shape": [], "dtype": "int32"}, {"name": "StatefulPartitionedCall/sequential_48_1/conv2d_transpose_39_1/conv_transpose/input_sizes/2", "shape": [], "dtype": "int32"}, {"name": "StatefulPartitionedCall/sequential_48_1/conv2d_transpose_39_1/conv_transpose/input_sizes/3", "shape": [], "dtype": "int32"}, {"name": "StatefulPartitionedCall/sequential_48_1/conv2d_transpose_39_1/conv_transpose/ReadVariableOp", "shape": [3, 3, 32, 64], "dtype": "float32"}, {"name": "StatefulPartitionedCall/sequential_48_1/conv2d_transpose_38_1/strided_slice/stack", "shape": [1], "dtype": "int32"}, {"name": "StatefulPartitionedCall/sequential_48_1/conv2d_transpose_38_1/strided_slice/stack_1", "shape": [1], "dtype": "int32"}, {"name": "StatefulPartitionedCall/sequential_48_1/conv2d_transpose_38_1/strided_slice/stack_2", "shape": [1], "dtype": "int32"}, {"name": "StatefulPartitionedCall/sequential_48_1/conv2d_transpose_38_1/conv_transpose/input_sizes/1", "shape": [], "dtype": "int32"}, {"name": "StatefulPartitionedCall/sequential_48_1/conv2d_transpose_38_1/conv_transpose/input_sizes/2", "shape": [], "dtype": "int32"}, {"name": "StatefulPartitionedCall/sequential_48_1/conv2d_transpose_38_1/conv_transpose/input_sizes/3", "shape": [], "dtype": "int32"}, {"name": "StatefulPartitionedCall/sequential_48_1/conv2d_transpose_38_1/conv_transpose/ReadVariableOp", "shape": [3, 3, 64, 64], "dtype": "float32"}, {"name": "StatefulPartitionedCall/sequential_48_1/dense_63_1/Cast/ReadVariableOp", "shape": [2, 3136], "dtype": "float32"}, {"name": "StatefulPartitionedCall/sequential_48_1/dense_63_1/Add/ReadVariableOp", "shape": [3136], "dtype": "float32"}, {"name": "StatefulPartitionedCall/sequential_48_1/reshape_13_1/strided_slice/stack", "shape": [1], "dtype": "int32"}, {"name": "StatefulPartitionedCall/sequential_48_1/reshape_13_1/strided_slice/stack_1", "shape": [1], "dtype": "int32"}, {"name": "StatefulPartitionedCall/sequential_48_1/reshape_13_1/strided_slice/stack_2", "shape": [1], "dtype": "int32"}, {"name": "StatefulPartitionedCall/sequential_48_1/reshape_13_1/Reshape/shape/1", "shape": [], "dtype": "int32"}, {"name": "StatefulPartitionedCall/sequential_48_1/reshape_13_1/Reshape/shape/2", "shape": [], "dtype": "int32"}, {"name": "StatefulPartitionedCall/sequential_48_1/reshape_13_1/Reshape/shape/3", "shape": [], "dtype": "int32"}, {"name": "StatefulPartitionedCall/sequential_48_1/conv2d_transpose_38_1/Reshape", "shape": [1, 1, 1, 64], "dtype": "float32"}, {"name": "StatefulPartitionedCall/sequential_48_1/conv2d_transpose_39_1/Reshape", "shape": [1, 1, 1, 32], "dtype": "float32"}, {"name": "StatefulPartitionedCall/sequential_48_1/conv2d_transpose_40_1/Reshape", "shape": [1, 1, 1, 1], "dtype": "float32"}]}]}