{"cells": [{"cell_type": "code", "execution_count": 1, "metadata": {}, "outputs": [{"name": "stderr", "output_type": "stream", "text": ["2024-09-03 16:04:01.934698: I tensorflow/core/platform/cpu_feature_guard.cc:210] This TensorFlow binary is optimized to use available CPU instructions in performance-critical operations.\n", "To enable the following instructions: AVX2 FMA, in other operations, rebuild TensorFlow with the appropriate compiler flags.\n"]}], "source": ["import keras\n", "import PIL\n", "import matplotlib.pyplot as plt\n", "import numpy as np"]}, {"cell_type": "code", "execution_count": 2, "metadata": {}, "outputs": [], "source": ["(x_train, y_train), (x_test, y_test) = keras.datasets.mnist.load_data()"]}, {"cell_type": "code", "execution_count": 3, "metadata": {}, "outputs": [{"data": {"image/png": "iVBORw0KGgoAAAANSUhEUgAAABwAAAAcCAAAAABXZoBIAAABAElEQVR4nGNgGMyAWUhIqK5jvdSy/9/rGRgYGFhgEnJsVjYCwQwMDAxPJgV+vniQgYGBgREqZ7iXH8r6l/SV4dn7m8gmCt3++/fv37/Htn3/iMW+gDnZf/+e5WbQnoXNNXyMs/5GoQoxwVmf/n9kSGFiwAW49/11wynJoPzx4YIcRlyygR/+/i2XxCWru+vv32nSuGQFYv/83Y3b4p9/fzpAmSyoMnohpiwM1w5h06Q+5enfv39/bcMiJVF09+/fv39P+mFKiTtd/fv3799jgZiBJLT69t+/f/8eDuDEkDJf8+jv379/v7Ryo4qzMDAwMAQGMjBc3/y35wM2V1IfAABFF16Aa0wAOwAAAABJRU5ErkJggg==", "text/plain": ["<PIL.Image.Image image mode=L size=28x28>"]}, "execution_count": 3, "metadata": {}, "output_type": "execute_result"}], "source": ["img = PIL.Image.fromarray(x_train[0])\n", "img"]}, {"cell_type": "code", "execution_count": 4, "metadata": {}, "outputs": [], "source": ["img.save(\"images/5.png\", format=\"png\")"]}, {"cell_type": "code", "execution_count": 13, "metadata": {}, "outputs": [{"data": {"text/plain": ["27525"]}, "execution_count": 13, "metadata": {}, "output_type": "execute_result"}], "source": ["x_train[0].reshape(-1, 784).sum()"]}, {"cell_type": "code", "execution_count": 5, "metadata": {}, "outputs": [{"data": {"image/png": "iVBORw0KGgoAAAANSUhEUgAAABwAAAAcCAAAAABXZoBIAAAA/0lEQVR4nGNgGHhgPP/vfCMccgbv/vz58xa7nNnjv3/ev/xjyYYpxWXz4M/fP6dC/vytgggwIUnOPCDDwMBgxHOQQRdD0tibkfFQKeOL85OYGLG5ZTOPd6UoA8Pfz2gOVlv69+WFEAj775+lKHLsm/58cBeWgUkeRpG0/PPHHs5Blzz2dx+C8//vEWTX+hj834SQ/Pf/ArLG0D/PJOHWt//dxYMqeR8u1/znoTsDquREKMtg6Z+1DKgg7O9DCKPo3d9FaHIMoX9+TjKQDd308O/95RaYkn/+PL3+58+fI03oUgwMMsf//Pn758/LiZhSDAwMkg1//v7pVcUqR1cAAKxwbkTVIzd2AAAAAElFTkSuQmCC", "text/plain": ["<PIL.Image.Image image mode=L size=28x28>"]}, "execution_count": 5, "metadata": {}, "output_type": "execute_result"}], "source": ["img = PIL.Image.fromarray(x_train[1])\n", "img"]}, {"cell_type": "code", "execution_count": 6, "metadata": {}, "outputs": [{"data": {"image/png": "iVBORw0KGgoAAAANSUhEUgAAABwAAAAcCAAAAABXZoBIAAAA1ElEQVR4nGNgGArA+YU6AwMDAwMTAwMDg10gqqTpGQaEpEMQihyTohwjgndnMYqk9L9FSDqZUE2dw3AbIaknjirJz7AbIenFiSInrsjwFCGpznAVWbJH/NZnCIuFgYGBgeE0XIbPI8aNofkDsqQQAwODPpOzDFs00/eTP1nOQlUyMjAwTEv/8IiBQY/xz7drJ88cfPlEkI0BoTProRUDA8OjjddOMDAwMKSJ3mPACVb+64QxmbBIb8AnyYBHklEVj+R/JjySDJb4jMVj5/b/OB1IJQAAg3ksR3QPgSAAAAAASUVORK5CYII=", "text/plain": ["<PIL.Image.Image image mode=L size=28x28>"]}, "execution_count": 6, "metadata": {}, "output_type": "execute_result"}], "source": ["img = PIL.Image.fromarray(x_train[2])\n", "img"]}, {"cell_type": "code", "execution_count": 7, "metadata": {}, "outputs": [{"data": {"image/png": "iVBORw0KGgoAAAANSUhEUgAAABwAAAAcCAAAAABXZoBIAAAAnElEQVR4nGNgGPyg5u9/e1xyCV9+/7WDMJkwJOXZcRvq8ub3ZXkO7HI2T37/jsOlcfbfv3txyYn8/f3aCYecwtm/v+twacz4/XcHPw65gA+/D4rjMvTv37/zcRk6/ffv3+o45Azu/v69BpfGV79/H+HBJfn39+9IXHLz///9K4/Lxid/v/fgCHAGh99/76CLYcYnNskbx/ApoyoAAGeYO0QsY6cRAAAAAElFTkSuQmCC", "text/plain": ["<PIL.Image.Image image mode=L size=28x28>"]}, "execution_count": 7, "metadata": {}, "output_type": "execute_result"}], "source": ["img = PIL.Image.fromarray(x_train[3])\n", "img.save(\"images/3.png\")\n", "img"]}, {"cell_type": "code", "execution_count": 8, "metadata": {}, "outputs": [{"data": {"image/png": "iVBORw0KGgoAAAANSUhEUgAAABwAAAAcCAAAAABXZoBIAAAA1ElEQVR4nN3QPwtBYRQG8EMU0e0uZLIw+QKXRZlMGC0GX8CglE0pk0VxPwQmE5YrJYPVIjYMlImSwXNiMOi97319AM/6O6fzh+g/Y5hr5mrRNByseAZba4D7EnlSN8wy3uAYXJOwDEw0ohKwD9mtxehqRLQBCnZr8GPkJ/Ll79y0m37GiIjiK2AQsGMYiIbryyvjmZO20U9gAIcjTg43GhfethOROToO+En6xRUlZhnSjd+I6BY7xVIRY79w4XapR9IOSTWWYSWUqE0xlH771R7UrULefm5U2pxVCt0AAAAASUVORK5CYII=", "text/plain": ["<PIL.Image.Image image mode=L size=28x28>"]}, "execution_count": 8, "metadata": {}, "output_type": "execute_result"}], "source": ["img = PIL.Image.fromarray(x_train[4])\n", "img"]}, {"cell_type": "code", "execution_count": 9, "metadata": {}, "outputs": [{"data": {"image/png": "iVBORw0KGgoAAAANSUhEUgAAABwAAAAcCAAAAABXZoBIAAABD0lEQVR4nGNgGGSAEY3Py+Mt1vsTq1LF6Rf+/PkzCZuUxowvf/4+uPznhQaGFP+M93/+/Lkhr/rnjw2GZMKfP3/+3JRlQJJkgkuGMjA8WO36mAHJTBY4KzVt151XDAwM4ti9BQFzEcayoEjkcTP+12U4dhxTC5fp5r9////9+0QZQ4rV7PGfz09Wffrz53kpG5ocm9+fP7XWDEIX/vz58yecHVVf+58/WwQYRE///d649s+fHU6GhnA55o4/H7MEGUxP/LnhyMDnsfjjnz/34ZKZfz5FCHmu+vKnTpaBgYGBIXLLFlW45PM/X8/e+PPnTw0zFo+f//Pnz59NJSqovoZGNm+A0at5739h0Ta4AABroXIjERrLHgAAAABJRU5ErkJggg==", "text/plain": ["<PIL.Image.Image image mode=L size=28x28>"]}, "execution_count": 9, "metadata": {}, "output_type": "execute_result"}], "source": ["\n", "img = PIL.Image.fromarray(x_train[5])\n", "img"]}, {"cell_type": "code", "execution_count": 11, "metadata": {}, "outputs": [{"data": {"image/png": "iVBORw0KGgoAAAANSUhEUgAAABwAAAAcCAAAAABXZoBIAAABEklEQVR4nM2RMS+DURiFn/ullKXC1KWJyVId2ARBQpqUHyBRC0NjsPsPNktj0F9QEgYiIvEDJG3CYhEpMTBI2qEk5+YzfP1uuD6bwVnum3ve877n5IV/jLH8Vmittfao36fyuw8tWUmSahmPPJEUk5oGIOXIixIvNRMyNZewMZXLZQEyLame9pR6jN7iMDx9JFtevZTk+4mwdtuVdD2IN3Z0fRFmQmjvnHY9TeE+jnLs/gJXGWOMCYwxKyUXIC5u5svn78DmdrJRAIYkpwx8svizv2+5536j/UUZYfZMOYCR8pvUWXAeAWiOU+0AS5MhV9XD78pm71Kyz/sD/sqJA0nSXWOvkBAgXXlVvZL9Jd4f4xPJmHJ5CeNkqwAAAABJRU5ErkJggg==", "text/plain": ["<PIL.Image.Image image mode=L size=28x28>"]}, "execution_count": 11, "metadata": {}, "output_type": "execute_result"}], "source": ["img = PIL.Image.fromarray(x_train[7])\n", "img"]}, {"cell_type": "code", "execution_count": 12, "metadata": {}, "outputs": [], "source": ["save = [1,2,3,4,5,7]\n", "for s in save:\n", "\timg = PIL.Image.fromarray(x_train[s])\n", "\timg.save(f\"images/{s}.png\")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": []}], "metadata": {"kernelspec": {"display_name": "Python 3", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.10.14"}}, "nbformat": 4, "nbformat_minor": 2}