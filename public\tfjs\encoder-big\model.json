{"format": "graph-model", "generatedBy": "2.16.2", "convertedBy": "TensorFlow.js Converter v4.20.0", "signature": {"inputs": {"inputs": {"name": "inputs:0", "dtype": "DT_FLOAT", "tensorShape": {"dim": [{"size": "-1"}, {"size": "28"}, {"size": "28"}, {"size": "1"}]}}}, "outputs": {"output_0": {"name": "Identity:0", "dtype": "DT_FLOAT", "tensorShape": {"dim": [{"size": "-1"}, {"size": "4"}]}}}}, "modelTopology": {"node": [{"name": "StatefulPartitionedCall/sequential_47_1/conv2d_53_1/convolution/ReadVariableOp", "op": "Const", "attr": {"value": {"tensor": {"dtype": "DT_FLOAT", "tensorShape": {"dim": [{"size": "3"}, {"size": "3"}, {"size": "1"}, {"size": "32"}]}}}, "dtype": {"type": "DT_FLOAT"}}}, {"name": "StatefulPartitionedCall/sequential_47_1/conv2d_53_1/Reshape", "op": "Const", "attr": {"value": {"tensor": {"dtype": "DT_FLOAT", "tensorShape": {"dim": [{"size": "1"}, {"size": "1"}, {"size": "1"}, {"size": "32"}]}}}, "dtype": {"type": "DT_FLOAT"}}}, {"name": "StatefulPartitionedCall/sequential_47_1/conv2d_54_1/convolution/ReadVariableOp", "op": "Const", "attr": {"value": {"tensor": {"dtype": "DT_FLOAT", "tensorShape": {"dim": [{"size": "3"}, {"size": "3"}, {"size": "32"}, {"size": "64"}]}}}, "dtype": {"type": "DT_FLOAT"}}}, {"name": "StatefulPartitionedCall/sequential_47_1/conv2d_54_1/Reshape", "op": "Const", "attr": {"value": {"tensor": {"dtype": "DT_FLOAT", "tensorShape": {"dim": [{"size": "1"}, {"size": "1"}, {"size": "1"}, {"size": "64"}]}}}, "dtype": {"type": "DT_FLOAT"}}}, {"name": "StatefulPartitionedCall/sequential_47_1/flatten_22_1/Reshape/shape", "op": "Const", "attr": {"value": {"tensor": {"dtype": "DT_INT32", "tensorShape": {"dim": [{"size": "2"}]}}}, "dtype": {"type": "DT_INT32"}}}, {"name": "StatefulPartitionedCall/sequential_47_1/dense_61_1/Cast/ReadVariableOp", "op": "Const", "attr": {"value": {"tensor": {"dtype": "DT_FLOAT", "tensorShape": {"dim": [{"size": "3136"}, {"size": "16"}]}}}, "dtype": {"type": "DT_FLOAT"}}}, {"name": "StatefulPartitionedCall/sequential_47_1/dense_61_1/Add/ReadVariableOp", "op": "Const", "attr": {"value": {"tensor": {"dtype": "DT_FLOAT", "tensorShape": {"dim": [{"size": "16"}]}}}, "dtype": {"type": "DT_FLOAT"}}}, {"name": "StatefulPartitionedCall/sequential_47_1/dense_62_1/Cast/ReadVariableOp", "op": "Const", "attr": {"value": {"tensor": {"dtype": "DT_FLOAT", "tensorShape": {"dim": [{"size": "16"}, {"size": "4"}]}}}, "dtype": {"type": "DT_FLOAT"}}}, {"name": "StatefulPartitionedCall/sequential_47_1/dense_62_1/Add/ReadVariableOp", "op": "Const", "attr": {"value": {"tensor": {"dtype": "DT_FLOAT", "tensorShape": {"dim": [{"size": "4"}]}}}, "dtype": {"type": "DT_FLOAT"}}}, {"name": "inputs", "op": "Placeholder", "attr": {"shape": {"shape": {"dim": [{"size": "-1"}, {"size": "28"}, {"size": "28"}, {"size": "1"}]}}, "dtype": {"type": "DT_FLOAT"}}}, {"name": "StatefulPartitionedCall/sequential_47_1/conv2d_53_1/Relu", "op": "_FusedConv2D", "input": ["inputs", "StatefulPartitionedCall/sequential_47_1/conv2d_53_1/convolution/ReadVariableOp", "StatefulPartitionedCall/sequential_47_1/conv2d_53_1/Reshape"], "device": "/device:CPU:0", "attr": {"num_host_args": {"i": "0"}, "data_format": {"s": "TkhXQw=="}, "filter_format": {"s": "SFdJTw=="}, "strides": {"list": {"i": ["1", "2", "2", "1"]}}, "num_args": {"i": "1"}, "fused_ops": {"list": {"s": ["Qmlhc0FkZA==", "UmVsdQ=="]}}, "TArgs": {"list": {"type": ["DT_FLOAT"]}}, "padding": {"s": "U0FNRQ=="}, "use_cudnn_on_gpu": {"b": true}, "leakyrelu_alpha": {"f": 0.2}, "epsilon": {"f": 0.0}, "T": {"type": "DT_FLOAT"}, "explicit_paddings": {"list": {}}, "dilations": {"list": {"i": ["1", "1", "1", "1"]}}}}, {"name": "StatefulPartitionedCall/sequential_47_1/conv2d_54_1/Relu", "op": "_FusedConv2D", "input": ["StatefulPartitionedCall/sequential_47_1/conv2d_53_1/Relu", "StatefulPartitionedCall/sequential_47_1/conv2d_54_1/convolution/ReadVariableOp", "StatefulPartitionedCall/sequential_47_1/conv2d_54_1/Reshape"], "device": "/device:CPU:0", "attr": {"num_host_args": {"i": "0"}, "data_format": {"s": "TkhXQw=="}, "filter_format": {"s": "SFdJTw=="}, "strides": {"list": {"i": ["1", "2", "2", "1"]}}, "num_args": {"i": "1"}, "fused_ops": {"list": {"s": ["Qmlhc0FkZA==", "UmVsdQ=="]}}, "TArgs": {"list": {"type": ["DT_FLOAT"]}}, "padding": {"s": "U0FNRQ=="}, "use_cudnn_on_gpu": {"b": true}, "leakyrelu_alpha": {"f": 0.2}, "epsilon": {"f": 0.0}, "T": {"type": "DT_FLOAT"}, "explicit_paddings": {"list": {}}, "dilations": {"list": {"i": ["1", "1", "1", "1"]}}}}, {"name": "StatefulPartitionedCall/sequential_47_1/flatten_22_1/Reshape", "op": "Reshape", "input": ["StatefulPartitionedCall/sequential_47_1/conv2d_54_1/Relu", "StatefulPartitionedCall/sequential_47_1/flatten_22_1/Reshape/shape"], "attr": {"T": {"type": "DT_FLOAT"}, "Tshape": {"type": "DT_INT32"}}}, {"name": "StatefulPartitionedCall/sequential_47_1/dense_61_1/Relu", "op": "_FusedMatMul", "input": ["StatefulPartitionedCall/sequential_47_1/flatten_22_1/Reshape", "StatefulPartitionedCall/sequential_47_1/dense_61_1/Cast/ReadVariableOp", "StatefulPartitionedCall/sequential_47_1/dense_61_1/Add/ReadVariableOp"], "device": "/device:CPU:0", "attr": {"transpose_a": {"b": false}, "transpose_b": {"b": false}, "fused_ops": {"list": {"s": ["Qmlhc0FkZA==", "UmVsdQ=="]}}, "num_args": {"i": "1"}, "leakyrelu_alpha": {"f": 0.2}, "epsilon": {"f": 0.0}, "T": {"type": "DT_FLOAT"}}}, {"name": "StatefulPartitionedCall/sequential_47_1/dense_62_1/Add", "op": "_FusedMatMul", "input": ["StatefulPartitionedCall/sequential_47_1/dense_61_1/Relu", "StatefulPartitionedCall/sequential_47_1/dense_62_1/Cast/ReadVariableOp", "StatefulPartitionedCall/sequential_47_1/dense_62_1/Add/ReadVariableOp"], "device": "/device:CPU:0", "attr": {"transpose_a": {"b": false}, "transpose_b": {"b": false}, "fused_ops": {"list": {"s": ["Qmlhc0FkZA=="]}}, "num_args": {"i": "1"}, "leakyrelu_alpha": {"f": 0.2}, "epsilon": {"f": 0.0}, "T": {"type": "DT_FLOAT"}}}, {"name": "Identity", "op": "Identity", "input": ["StatefulPartitionedCall/sequential_47_1/dense_62_1/Add"], "attr": {"T": {"type": "DT_FLOAT"}}}], "library": {}, "versions": {"producer": 1766}}, "weightsManifest": [{"paths": ["group1-shard1of1.bin"], "weights": [{"name": "StatefulPartitionedCall/sequential_47_1/conv2d_53_1/convolution/ReadVariableOp", "shape": [3, 3, 1, 32], "dtype": "float32"}, {"name": "StatefulPartitionedCall/sequential_47_1/conv2d_53_1/Reshape", "shape": [1, 1, 1, 32], "dtype": "float32"}, {"name": "StatefulPartitionedCall/sequential_47_1/conv2d_54_1/convolution/ReadVariableOp", "shape": [3, 3, 32, 64], "dtype": "float32"}, {"name": "StatefulPartitionedCall/sequential_47_1/conv2d_54_1/Reshape", "shape": [1, 1, 1, 64], "dtype": "float32"}, {"name": "StatefulPartitionedCall/sequential_47_1/flatten_22_1/Reshape/shape", "shape": [2], "dtype": "int32"}, {"name": "StatefulPartitionedCall/sequential_47_1/dense_61_1/Cast/ReadVariableOp", "shape": [3136, 16], "dtype": "float32"}, {"name": "StatefulPartitionedCall/sequential_47_1/dense_61_1/Add/ReadVariableOp", "shape": [16], "dtype": "float32"}, {"name": "StatefulPartitionedCall/sequential_47_1/dense_62_1/Cast/ReadVariableOp", "shape": [16, 4], "dtype": "float32"}, {"name": "StatefulPartitionedCall/sequential_47_1/dense_62_1/Add/ReadVariableOp", "shape": [4], "dtype": "float32"}]}]}