name: Deploy

on:
    push:
        branches:
            - main

jobs:
    build:
        name: Build
        runs-on: ubuntu-latest

        steps:
            - name: Checkout
              uses: actions/checkout@v4

            - uses: pnpm/action-setup@v4
              name: Install pnpm
              with:
                  version: 9
                  run_install: false

            - name: Install Node.js
              uses: actions/setup-node@v4
              with:
                  node-version: 20
                  cache: "pnpm"

            - name: Install dependencies
              run: pnpm install

            - name: Build
              run: pnpm build

            - name: Upload production-ready build files
              uses: actions/upload-artifact@v4
              with:
                  name: production-files
                  path: ./dist

    deploy:
        name: Deploy
        needs: build
        runs-on: ubuntu-latest
        if: github.ref == 'refs/heads/main'

        steps:
            - name: Download artifact
              uses: actions/download-artifact@v4
              with:
                  name: production-files
                  path: ./dist

            - name: Deploy to GitHub Pages
              uses: peaceiris/actions-gh-pages@v4
              with:
                  github_token: ${{ secrets.GITHUB_TOKEN }}
                  publish_dir: ./dist
